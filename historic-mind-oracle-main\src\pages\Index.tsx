
import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Crown, Sparkles, Play } from "lucide-react";
import GameScreen from "@/components/GameScreen";

const Index = () => {
  const [gameStarted, setGameStarted] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  if (gameStarted) {
    return <GameScreen onBackToMenu={() => setGameStarted(false)} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-20 left-20 w-32 h-32 bg-yellow-400/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-purple-400/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-blue-400/5 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-10 right-10 w-20 h-20 bg-green-400/10 rounded-full blur-lg animate-pulse delay-500"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 bg-pink-400/10 rounded-full blur-xl animate-pulse delay-1500"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 min-h-screen flex flex-col items-center justify-center">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <Crown className="w-16 h-16 text-yellow-400 mr-4 animate-bounce" />
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-yellow-400 via-yellow-200 to-yellow-400 bg-clip-text text-transparent drop-shadow-2xl">
              اعرف شخصيتك
            </h1>
            <Crown className="w-16 h-16 text-yellow-400 ml-4 animate-bounce delay-500" />
          </div>
          <p className="text-xl md:text-2xl text-purple-200 font-light mb-4">
            🏛️ رحلة عبر التاريخ مع أعظم الشخصيات 🏛️
          </p>
          <p className="text-lg md:text-xl text-purple-300 font-light">
            فكر في أي شخصية تاريخية مشهورة وسأحاول تخمينها خلال 20 سؤال!
          </p>
        </div>

        {/* Main Card */}
        <Card className="bg-black/40 backdrop-blur-lg border-purple-500/30 p-8 md:p-12 max-w-lg w-full text-center shadow-2xl ring-1 ring-purple-400/20">
          <div className="space-y-6">
            <div className="flex justify-center">
              <Sparkles className="w-16 h-16 text-yellow-400 animate-spin" />
            </div>

            <h2 className="text-3xl font-bold text-white mb-2">
              🎯 هل أنت مستعد للتحدي؟
            </h2>

            <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg p-4 border border-purple-400/30">
              <p className="text-purple-200 leading-relaxed text-lg">
                ✨ اختر أي شخصية تاريخية من أي عصر أو حضارة
              </p>
              <p className="text-purple-300 text-sm mt-2">
                من الفراعنة إلى العلماء المعاصرين، من القادة إلى الفلاسفة
              </p>
            </div>

            <div className="space-y-4">
              <Button
                onClick={() => setGameStarted(true)}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Play className="w-5 h-5 mr-2" />
                ابدأ اللعبة
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowInstructions(!showInstructions)}
                className="w-full border-purple-400 text-purple-200 hover:bg-purple-600/20 py-3 rounded-xl transition-all duration-300"
              >
                كيف ألعب؟
              </Button>
            </div>

            {showInstructions && (
              <div className="mt-6 p-4 bg-purple-800/20 rounded-xl border border-purple-500/30">
                <h3 className="text-lg font-semibold text-yellow-400 mb-3">طريقة اللعب:</h3>
                <ul className="text-sm text-purple-200 space-y-2 text-right">
                  <li>• فكر في أي شخصية تاريخية مشهورة</li>
                  <li>• أجب على الأسئلة بنعم أو لا فقط</li>
                  <li>• سأحاول تخمين الشخصية خلال 20 سؤال</li>
                  <li>• كن صادقاً في إجاباتك!</li>
                </ul>
              </div>
            )}
          </div>
        </Card>

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-purple-300/80 text-sm">
            مدعوم بالذكاء الاصطناعي • اكتشف الشخصيات التاريخية
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
