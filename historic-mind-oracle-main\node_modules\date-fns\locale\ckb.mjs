import { formatDistance } from "./ckb/_lib/formatDistance.mjs";
import { formatLong } from "./ckb/_lib/formatLong.mjs";
import { formatRelative } from "./ckb/_lib/formatRelative.mjs";
import { localize } from "./ckb/_lib/localize.mjs";
import { match } from "./ckb/_lib/match.mjs";

/**
 * @type {Locale}
 * @category Locales
 * @summary Central Kurdish locale.
 * @language Central Kurdish
 * @iso-639-2 kur
 * <AUTHOR> [@Revan99]{@link https://github.com/Revan99}
 */
export const ckb = {
  code: "ckb",
  formatDistance,
  formatLong,
  formatRelative,
  localize,
  match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ckb;
