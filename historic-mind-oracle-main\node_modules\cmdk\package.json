{"name": "cmdk", "version": "1.0.0", "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-primitive": "1.0.3"}, "devDependencies": {"@types/react": "18.0.15"}, "sideEffects": false, "scripts": {"build": "tsup src", "dev": "tsup src --watch"}}